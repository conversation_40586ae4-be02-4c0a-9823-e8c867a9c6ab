{% extends 'partials/commons/skeleton-slider.twig' %}

{% block options %}
    class="default-slider"
    data-s-speed="500" 
    data-loop="false" 
    data-slides-per-view="1.2" 
    data-space-between="var(--grid-gutter)"
    data-breakpoints='{
        "1024":{"slidesPerView":3.2}, 
        "768":{"slidesPerView":2.2}, 
        "320":{"slidesPerView":1.2}
    }'
{% endblock %}

{% block title %}
    <h2 class="-no-space">{{ title }}</h2>
{% endblock %}

{% block nav %}
    <div class="swiper-navigation">
        <button class="prev" data-swiper="prev" title="{{ __('Previous slide', 'enero') }}"><i class="icon-arrow-left"></i></button>
        <button class="next" data-swiper="next" title="{{ __('Next slide', 'enero') }}"><i class="icon-arrow-right"></i></button>
    </div>
{% endblock %}

{% block slides %}
		{% for slide in slides %}
			<div class="swiper-slide">
				<div class="image">
                    <img src="{{ slide.thumbnail.src }}" alt="">
                    <div class="inner">
                        <p class="title -no-space">{{ slide.title }}</p>
                    </div>
                </div>
			</div>
		{% endfor %}
{% endblock %}

