<footer class="footer -dark">
	<div class="texture"></div>
	<div class="grid">
		<ul class="main-footer -no-list-style col-12 col-t-lg-9 grid">
			{% for item in menu_footer.items %}
				<li role="none" class="col-12 col-t-4 {{ item.classes | join(' ') }}">
					<p target="{{ item.target ? item.target : '_self' }}" href="{{ item.link }}" class="section-title -h6">
						{{ item.title }}
					</p>
					{% if item.children %}
						<ul class="sub-menu -no-list-style">
							{% for child in item.children %}
								<li role="none" class="menu-item child-item {{ child.classes | join(' ') }}">
									<a target="{{ child.target ? child.target : '_self' }}" href="{{ child.link }}" class="--small">
										{{ child.title }}
									</a>
								</li>
							{% endfor %}
						</ul>
					{% endif %}
				</li>
			{% endfor %}
		</ul>

		<ul class="side-footer -no-list-style col-12 col-t-lg-3">
			{% for item in menu_footer_side.items %}
				<li role="none" class="menu-item primary-item {{ item.classes | join(' ') }}">
					<a target="{{ item.target ? item.target : '_self' }}" href="{{ item.link }}" class="-h6">
						{{ item.title }}
					</a>
				</li>
			{% endfor %}
		</ul>
	</div>

	<div class="logo">
		{% include 'partials/snippets/logo.twig' %}
	</div>

    <div class="sub-footer">

		<div class="inner">
			<p class="enero --caption -no-space">
				©
				{{ 'now'|date('Y') }}
				{{ __('Enero Solutions Inc. Tous droits réservés.', 'enero') }}
			</p>

			<ul class="menu -no-list-style">
				{% for item in menu_sub_footer.items %}
					<li role="none" class="menu-item primary-item {{ item.classes | join(' ') }}">
						<a target="{{ item.target ? item.target : '_self' }}" href="{{ item.link }}" class="--caption">
							{{ item.title }}
						</a>
					</li>
				{% endfor %}
			</ul>

			<p class="copyright --caption -no-space">
				<span>Site web par <a href="https://www.kryzalid.net" target="_blank -clean">Kryzalid</a></span>
			</p>
		</div>

		<div class="socials">
			<p class="-no-space --caption">{{ __('Suivez-nous sur ', 'enero') }}</p>
			{% for key,social in options['socials_media'] %}
				<a href="{{ social }}" aria-label="{{ __('Visiter le compte %key% d\'Enero', 'sqdi')|replace({'%key%': key}) }}" target="_blank" class="social -clean">
					<i class="icon-{{ key }}"></i>
				</a>
			{% endfor %}
		</div>

    </div>
</footer>