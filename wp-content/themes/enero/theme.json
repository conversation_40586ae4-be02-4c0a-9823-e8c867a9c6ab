{"$schema": "https://raw.githubusercontent.com/WordPress/gutenberg/trunk/schemas/json/theme.json", "version": 3, "settings": {"appearanceTools": true, "color": {"background": true, "text": true, "custom": true, "customDuotone": false, "customGradient": false, "defaultGradients": false, "defaultPalette": false, "palette": [{"name": "Brand Blue Primary", "slug": "brand-blue-primary", "color": "var(--brand-blue-primary)"}, {"name": "Brand Blue Dark", "slug": "brand-blue-dark", "color": "var(--brand-blue-dark)"}, {"name": "Brand Blue Navy", "slug": "brand-blue-navy", "color": "var(--brand-blue-navy)"}, {"name": "Brand Blue Steel", "slug": "brand-blue-steel", "color": "var(--brand-blue-steel)"}, {"name": "Brand Light Grey", "slug": "brand-light-grey", "color": "var(--brand-light-grey)"}]}, "layout": {"contentSize": "100%"}, "spacing": {"blockGap": false, "margin": true, "padding": true, "customSpacingSize": false, "defaultSpacingSizes": false, "units": ["px", "em", "rem", "vh", "vw", "%"], "spacingSizes": [{"size": "var(--spacing-small)", "slug": "small", "name": "Small"}, {"size": "var(--spacing-normal)", "slug": "normal", "name": "Normal"}, {"size": "var(--spacing-medium)", "slug": "medium", "name": "Medium"}, {"size": "var(--spacing-large)", "slug": "large", "name": "Large"}, {"size": "var(--spacing-xlarge)", "slug": "xlarge", "name": "<PERSON>L<PERSON>ge"}]}, "border": {"radius": true, "width": false, "style": false, "color": false}, "typography": {"fluid": true, "customFontSize": true, "dropCap": false, "defaultFontSizes": false, "fontFamilies": [{"name": "Primary", "slug": "primary", "fontFamily": "var(--font-primary)"}, {"name": "Title", "slug": "title", "fontFamily": "var(--font-title)"}, {"name": "CTA", "slug": "cta", "fontFamily": "var(--font-cta)"}], "fontSizes": [{"size": "var(--font-size-small)", "slug": "small", "name": "Small"}, {"size": "var(--font-size-body)", "slug": "body", "name": "Body"}, {"size": "var(--font-size-large)", "slug": "large", "name": "Large"}]}, "blocks": {"core/button": {"color": {"background": false, "text": false, "palette": [{"slug": "primaire", "color": "var(--color-btn-primary)", "name": "Primary"}, {"slug": "secondaire", "color": "var(--color-btn-secondary)", "name": "Secondary"}, {"slug": "texte", "color": "var(--color-btn-text)", "name": "Text"}]}, "spacing": {"padding": false, "margin": true, "spacingSizes": [{"size": "20px", "slug": "small", "name": "<PERSON>"}, {"size": "30px", "slug": "normal", "name": "Normal"}, {"size": "40px", "slug": "large", "name": "Large"}], "customSpacingSize": false}, "border": {"radius": false}, "typography": {"customFontSize": false, "fontSizes": []}}, "core/paragraph": {"color": {"text": true, "background": false}}, "core/column": {"color": {"text": false}}, "core/image": {"appearanceTools": false, "border": {"radius": false, "style": false, "width": false, "color": false}}, "core/quote": {"color": {"text": false, "background": false, "link": false}, "background": {"backgroundImage": false}}, "core/gallery": {"spacing": {"customSpacingSize": false, "blockGap": null}}, "core/table": {"color": {"background": false}, "border": {"width": false, "color": false, "radius": false, "style": false}}}, "useRootPaddingAwareAlignments": true}, "styles": {"color": {"background": "white", "text": "var(--color-text)"}, "typography": {"fontFamily": "var(--font-primary)", "fontSize": "var(--font-size-body)", "lineHeight": "var(--line-height-body)"}, "elements": {"link": {"color": {"text": "var(--color-link)"}, ":hover": {"color": {"text": "var(--color-link-hover)"}, "typography": {"textDecoration": "none"}}, ":active": {"color": {"text": "var(--color-link-hover)"}, "typography": {"textDecoration": "none"}}, "typography": {"textDecoration": "underline"}}, "button": {"color": {"background": "var(--color-btn-secondary)", "text": "var(--color-btn-text)"}, "border": {"radius": "var(--border-radius-medium)", "color": "var(--color-btn-primary)", "width": "0px"}, ":hover": {"color": {"text": "var(--color-btn-text)", "background": "var(--color-btn-primary)"}}}, "heading": {"typography": {"fontFamily": "var(--font-title)", "fontWeight": "500"}, "spacing": {"margin": {"top": "clamp(2.5625rem, 0.625rem + 2.7273vw, 2.5rem)", "bottom": "clamp(2.5625rem, 0.625rem + 2.7273vw, 2.5rem)"}}}, "h1": {"typography": {"fontSize": "var(--font-size-h1)", "lineHeight": "var(--line-height-h1)", "fontWeight": "400"}}, "h2": {"typography": {"fontSize": "var(--font-size-h2)", "lineHeight": "var(--line-height-h2)", "fontWeight": "400"}}, "h3": {"typography": {"fontSize": "var(--font-size-h3)", "lineHeight": "var(--line-height-h3)", "fontWeight": "400"}}, "h4": {"typography": {"fontSize": "var(--font-size-h4)", "lineHeight": "var(--line-height-h4)", "fontWeight": "400"}}, "h5": {"typography": {"fontSize": "var(--font-size-h5)", "lineHeight": "var(--line-height-h5)", "fontWeight": "500"}}, "h6": {"typography": {"fontSize": "var(--font-size-h6)", "lineHeight": "var(--line-height-h6)", "fontWeight": "500"}}}, "spacing": {"blockGap": "var(--spacing-normal) var(--grid-gutter)", "padding": {"top": "0px", "right": "var(--container-margin)", "bottom": "0px", "left": "var(--container-margin)"}}, "blocks": {"core/post-title": {"spacing": {"margin": {"bottom": "1.25rem", "top": "1.25rem"}}, "typography": {"fontFamily": "var(--font-title)", "fontSize": "var(--font-size-h2)", "fontWeight": "700", "lineHeight": "var(--line-height-h2)"}}, "core/paragraph": {"spacing": {"margin": {"top": "1em", "bottom": "1em"}}, "typography": {"fontSize": "var(--font-size-body)", "lineHeight": "var(--line-height-body)"}, "elements": {"link": {"typography": {"textDecoration": "underline", "fontWeight": "700"}, "color": {"text": "var(--color-link)"}, ":hover": {"color": {"text": "var(--color-link-hover)"}}}}}, "core/image": {"elements": {"caption": {"typography": {"fontSize": "var(--font-size-caption)", "lineHeight": "var(--line-height-caption)"}, "color": {"text": "var(--color-text-secondary)", "background": "transparent"}, "border": {"radius": "0"}, "spacing": {"padding": {"top": "15px", "bottom": "15px", "left": "20px", "right": "20px"}}}}, "border": {"radius": "var(--border-radius-medium)"}}, "core/cover": {"border": {"radius": "var(--border-radius-large)"}}, "core/separator": {"border": {"width": "1px 0px 0px 0px", "color": "var(--border-default)"}, "spacing": {"margin": {"top": "var(--spacing-normal)", "bottom": "var(--spacing-normal)"}}}, "core/group": {"spacing": {"margin": {"top": "var(--spacing-large)", "bottom": "var(--spacing-large)"}}}, "core/columns": {"spacing": {"margin": {"top": "var(--spacing-medium)", "bottom": "var(--spacing-medium)"}}}, "core/quote": {"border": {"width": "0px 0px 0px 4px", "color": "var(--color-sticky-title)", "style": "solid"}, "color": {"text": "var(--color-sticky-title)"}, "spacing": {"padding": {"left": "40px"}, "margin": {"top": "var(--spacing-medium)", "bottom": "var(--spacing-medium)"}}, "typography": {"fontSize": "var(--font-size-large)", "lineHeight": "var(--line-height-large)"}}}}, "templateParts": [{"area": "header", "name": "header", "title": "Header"}, {"area": "footer", "name": "footer", "title": "Footer"}, {"area": "uncategorized", "name": "comments", "title": "Comments"}, {"area": "uncategorized", "name": "post-meta", "title": "Post Meta"}]}