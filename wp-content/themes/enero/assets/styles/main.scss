@use 'sass:math';

@import 'commons.scss';

// 1. Import node modules Vendors css
@import '../../node_modules/swiper/swiper.scss';
@import '../../node_modules/swiper/modules/autoplay.scss';
// @import '../../node_modules/swiper/modules/effect-fade.scss';

// 2. Import fonts and icons
@import 'config/fonts';
@import 'config/icons';

// -------- HELPERS --------
@import 'utilities/helpers';
@import 'utilities/development-banner';

// 3. Set global page layout and containers, typography and links/buttons
@import 'common/grid';
@import 'common/typography';

// 4. <PERSON><PERSON> gutenberg blocks custom styling
@import 'common/gutenberg';
@import 'common/forms';
@import 'common/ajax';
@import 'common/loader';

// -------- PARTIALS --------

// Components
@import 'partials/header/header';
@import 'partials/header/header-panel';
@import 'partials/footer/footer';

// Commons
@import 'partials/commons/pagination';
@import 'partials/commons/preloader';
@import 'partials/commons/share-post';
@import 'partials/commons/pre-footer';

// Cards
// @import 'partials/cards/sample-card';

// Filters
@import 'partials/filters/filters';
@import 'partials/filters/filters-popup';

// Heroes
@import 'partials/heroes/page-hero';
@import 'partials/heroes/industry-hero';

// Lists
// @import 'partials/lists/sample-list';

// Sliders
@import 'partials/sliders/default-slider';

// Snippets
@import 'partials/snippets/links';
@import 'partials/snippets/hamburger';
@import 'partials/snippets/mouse-tracker';


// Blocks
@import 'blocks/content-accordion';
@import 'blocks/content-sticky-ctn';


// -------- PAGES --------
@import 'pages/home';
@import 'pages/404';
@import 'pages/careers';
@import 'pages/contact';
@import 'pages/formations';
@import 'pages/posts';
@import 'pages/works';


// -------- SINGLES --------
@import 'pages/singles/single-expertise';
@import 'pages/singles/single-industry';
@import 'pages/singles/single-posts';
@import 'pages/singles/single-process';
@import 'pages/singles/single-work';
@import 'pages/singles/single-page';



// -------- EXTRA --------

// 98. Style override for page printing 
@import 'common/print';

// 99. This file contains hacky code to eventually put elsewhere, load it at the very end
@import 'common/shame';