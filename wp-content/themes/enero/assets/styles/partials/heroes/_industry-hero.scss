.industry-hero {
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    height: 100dvh;
    position: relative;
    margin-top: calc(var(--header-height) * -1);
    
    .inner {
        padding-top: calc(rem(80px) + var(--header-height));
        padding-bottom: rem(80px);
        height: 100%;
    }

    .filter {
        position: absolute;
        height: 100%;
        width: 100%;
        top: 0;
        left: 0;

        &::before, &::after {
            content: '';
            position: absolute;
            height: 100%;
            width: 100%;
            left: 0;
            z-index: 1;
        }
        
        &:before {
            background: linear-gradient(0deg, rgba(2, 38, 61, 0.00) 53.72%, var(--blue-navy, #02263D) 100.75%);
        }

        &:after {
            background: linear-gradient(0deg, var(--blue-navy, #02263D) 2.1%, rgba(2, 38, 61, 0.00) 54.82%);
        }
    }

    h1, p {
        color: $white;
    }

    .title {
        align-self: end;
        z-index: 9;
    }

    .excerpt {
        z-index: 9;
    }

    @media (max-width: $tablet-lg) {
        height: 70dvh;

        .inner {
            padding-top: calc(rem(40px) + var(--header-height));
            padding-bottom: rem(40px);
            display: flex;
            flex-direction: column;
            justify-content: flex-end;

            .title {
                align-self: start;
            }
        }
    }
}