.default-slider {
    padding-top: rem(72px);
    padding-bottom: rem(72px);

    .swiper-wrapper {
        padding-top: rem(72px);
        padding-bottom: rem(72px);
    }

    .swiper-slide {
        
        .image {
            aspect-ratio: 1/1;
            position: relative;
            background: linear-gradient(180deg, rgba(0, 0, 0, 0.00) 0%, rgba(0, 0, 0, 0.40) 100%);

            // force remove default image reveal effect
            &.-reveal {
                clip-path: none;
            }

            img {
                @include img();
            }

            .inner {
                position: absolute;
                display: flex;
                align-items: flex-end;
                top: 0;
                padding: rem(32px);
                width: 100%;
                height: 100%;
                z-index: 9;
            }

            .title {
                color: $white;
                font-size: rem(28px);
                line-height: 128%;
                letter-spacing: -0.308px;
                font-family: var(--font-title);
            }
        }
    }

    .swiper-navigation {
        display: flex;
        justify-content: flex-end;
        gap: rem(5px);
        
        .prev, .next {
            border: 1px solid var(--border-default, rgba(87, 87, 86, 0.40));
            border-radius: var(--border-radius-small, 12px);
            width: rem(64px);
            height: rem(64px);
            display: flex;
            justify-content: center;
            align-items: center;
            transition: $transition;
            cursor: pointer;

            &:hover {
                background-color: var(--brand-blue-primary);
                i {
                    color: $white;
                }
            }
           
        }

        i {
            color: var(--brand-blue-primary);
            font-size: rem(20px);
            transition: $transition;
        }
    }
}