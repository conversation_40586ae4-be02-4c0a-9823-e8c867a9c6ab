.content-accordion-ctn {


    .accordion-item{
        border: 1px solid var(--border-default);
        border-radius: var(--border-radius-medium); 
    }

    .accordion-header {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: rs(20px, 40px) var(--spacing-medium);

        &:hover {
            cursor: pointer;
        }

        .accordion-title{
            margin: 0;
        }
    }

    .icon-plus {
        @include flex();
        font-size: rem(20px);
        &::after {
            @include icon('\e90d');
        }

        &::after {
            @include icon('\e903');
        }

        &::after, &::before {
            color: var(--brand-blue-primary);
        }
    }

    .inner {
        visibility: hidden;
        transition: height $default-speed $default-easing, visibility 1s ease;
        overflow: hidden;
        padding: 0 var(--spacing-medium);

        &:first-child { margin-top: 0; } // Remove any additional margin at the beginning of content

        > *:last-child { padding-bottom: rs(20px, 40px); } // Add padding at the end of content
    }

    &.-open {

        .inner{
            visibility: visible;
        }

        .icon-plus {
            &::before { opacity: 0; }
        }
    }
}