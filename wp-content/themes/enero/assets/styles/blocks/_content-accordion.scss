.content-accordion-ctn {
    border-bottom: 1px solid rgba($color: #fff, $alpha: .5);
    cursor: pointer;

    button {
        width: 100%;
    }

    &:last-of-type { border-bottom: 0; }

    .title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: rs(20px, 40px) 0;
        font-size: rs(20px, 24px);
        line-height: rs(28px, 32px);
        margin: 0;
    }

    .icon-plus {
        @include flex();
        position: relative;
        border-radius: 100%;
        background-color: $black;
        @include size(32px);

        &::after {
           @include icon('\e90b'); // Need to be changed
        }

        &::after, &::before {
            color: black;
            font-size: 13px;
            transition: $transition;
        }
    }

    .inner {
        visibility: hidden;
        transition: height $default-speed $default-easing, visibility 0.3s ease;
        overflow: hidden;

        &:first-child { margin-top: 0; } // Remove any additional margin at the beginning of content

        > *:last-child { padding-bottom: rs(20px, 40px); } // Add padding at the end of content
    }

    &.-open {

        .inner{
            visibility: visible;
        }

        .icon-plus {
            &::before { opacity: 0; }
        }
    }
}

.block-editor-block-list__layout {
    .content-sticky-image,.content-sticky-content{
        width: auto !important;
    }
}