/**
  * This file contains code related to typography - titles, paragraphs, text modifiers, etc
  */

h1, h2, h3, h4, h5, h6 {
  font-family: var(--font-title);
  color: var(--color-title); 
}

h1 {
  font-size: var(--font-size-h1);
  line-height: var(--line-height-h1);
  margin: rem(104px) 0 rem(56px) 0;
  letter-spacing: -3.6px;
  font-weight: 400;

  &.-small {
    font-size: var(--font-size-h1-small);
    line-height: var(--line-height-h1-small);
    letter-spacing: -3.2px;
    font-weight: 400;
  }
}

h2 {
  font-size: var(--font-size-h2);
  line-height: var(--line-height-h2);
  margin: rs(96px, 116px) 0 rs(40px, 40px) 0;
  letter-spacing: -2.5px;
  font-weight: 400;

  &.-large {
    font-size: var(--font-size-h2-large);
    line-height: var(--line-height-h2-large);
    letter-spacing: -2.5px;
    font-weight: 400;
  }

  &.-small {
    font-size: var(--font-size-h2-small);
    line-height: var(--line-height-h2-small);
    letter-spacing: -2px;
    font-weight: 400;
  }
}

h3 {
  font-size: var(--font-size-h3);
  line-height: var(--line-height-h3);
  margin: rem(96px) 0 rem(24px) 0;
  letter-spacing: -1.5px;
  font-weight: 400;
}

h4 {
  font-size: var(--font-size-h4);
  line-height: var(--line-height-h4);
  margin: rem(64px) 0 rem(24px) 0;
  letter-spacing: -0.33px;
  font-weight: 400;
}

h5 {
  font-size: var(--font-size-h5);
  line-height: var(--line-height-h5);
  margin: rem(40px) 0 rem(24px) 0;
  letter-spacing: -0.264px;
  font-weight: 500;
}

h6 {
  font-size: var(--font-size-h6);
  line-height: var(--line-height-h6);
  margin: rem(32px) 0 rem(16px) 0;
  letter-spacing: -0.242px;
  font-weight: 500;
}

.-h1 { @extend h1; }
.-h2 { @extend h2; }
.-h3 { @extend h3; }
.-h4 { @extend h4; }
.-h5 { @extend h5; }
.-h6 { @extend h6; }


// Paragraphs sizes

p{
  font-size: var(--font-size-body);
  line-height: var(--line-height-body);
}

.--large {
  font-size: var(--font-size-large);
  line-height: var(--line-height-large);
}

.--normal {
  font-size: var(---font-size-body);
  line-height: var(--line-height-body);
}

.--small {
  font-size: var(--font-size-small);
  line-height: var(--line-height-small);
}

.--caption {
  font-size: var(--font-size-caption);
  line-height: var(--line-height-caption);
}

// Eyebrow
.--eyebrow {
  font-size: var(--font-size-eyebrow);
  line-height: var(--line-height-eyebrow);
  color: var(--eyebrow);
}

.--eyebrow-small {
  font-size: var(--font-size-eyebrow-small);
  line-height: var(--line-height-eyebrow-small);
}

// Meta
.--meta {
  font-size: var(--font-size-meta);
  line-height: var(--line-height-meta);
}

// Cta sizes
.-cta-large {
  font-size: var(--font-size-cta-large);
  line-height: var(--line-height-cta-large);
  font-weight: 500;
  letter-spacing: -0.6px;
  font-family: var(--font-cta);
}

.-cta {
  font-size: var(--font-size-cta);
  line-height: var(--line-height-cta);
  font-weight: 500;
  letter-spacing: -0.6px;
  font-family: var(--font-cta);
}

.-cta-small {
  font-size: var(--font-size-cta-small);
  line-height: var(--line-height-cta-small);
}

// Tags
.-tag {
  font-size: var(--font-size-tag);
  line-height: var(--line-height-tag);

}
.-tag-large {
  font-size: var(--font-size-tag-large);
  line-height: var(--line-height-tag-large);
}

.title-section{
  @extend .-small;
  width: 100%;
  position: relative;
  color: var(--color-sticky-title);
  padding-left: rem(36px);

  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    width: rem(12px);
    height: rem(12px);
    border-radius: 50%;
    background-color: var(--color-sticky-title);
  }
}