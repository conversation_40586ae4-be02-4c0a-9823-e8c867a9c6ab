/**
  * This file contains code that overrides gutenberg blocks in admin and on pages
  * Gutenberg style must be inside theme.json : 
  * https://developer.wordpress.org/block-editor/how-to-guides/themes/global-settings-and-styles/
  * --------------------------------------------
  * But can be override here if necessary 
  */

.gutenberg {
  margin-bottom: rs(120px, 158px);

  // Full width
  .alignfull:not(.wp-block-image) {
    margin-right: calc(var(--container-margin) * -1);
    margin-left: calc(var(--container-margin) * -1);
    overflow: hidden;
  }

  // Make sure the image is always full height if parent column is set to stretch
  .is-vertically-aligned-stretch {
    .wp-block-image, img {
      height: 100%;
      object-fit: cover;
    }
  }

  .wp-block-cover {
    .wp-block-cover__inner-container > * { 
      padding-left: var(--container-margin) !important; 
      padding-right: var(--container-margin) !important;
    }
  }

  .wp-block-group.has-background:not([style*="padding"]) {
    padding: rs(40px, 72px) var(--spacing-medium);
    border-radius: var(--border-radius-medium);

    p{
      color: var(--color-text);
    }

    @media (max-width: $mobile-lg) {
      padding-left: rem(20px);
      padding-right: rem(20px);
    }
  }

  .wp-block-group.has-background:not([class*="-light-" i]) {
    @extend %dark-vars;
  }

  // fix 4 columns layout (break earlier)
  // .wp-container-core-columns-is-layout-4 {
  //   @media (max-width: $desktop-sm) and (min-width: 781px) {
  //     flex-wrap: wrap !important;
  //     .wp-block-column { 
  //       flex-basis: 40% !important; 
  //     }
  //   }
  // }

  // lists
  ul, ol {
    list-style: initial;
    padding: 0 1em;

    li{
      margin: 1em 0;
      &::marker {
        color: $black;
        font-size: 20px;
      }
    }
  }

  ol {
    list-style: number;
  }

  // fix 4 columns layout breaking on tablet
  // .wp-container-core-columns-is-layout-4 {
  //   @media (max-width: $desktop-sm) and (min-width: 781px) {
  //     flex-wrap: wrap !important;
  //     .wp-block-column { 
  //       flex-basis: 40% !important; 
  //     }
  //   }
  // }

  /**** Block Youtube ****/
  .wp-block-embed-youtube {
    //max-width: 992px;
    margin: 0px auto;

    .wp-block-embed__wrapper {
      position: relative;
      padding-bottom: 56.25%; /* 16:9 */
      padding-top: 25px;
      height: 0;

      iframe {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }
    }

    @media (max-width: 1500px) {
      max-width: 992px;
    }
  }

  // Table
  .wp-block-table {
    margin: var(--spacing-medium) 0;

    table {
      border-radius: $radius-sm;
      overflow: hidden;
    }

    td, th {
      border: none;
      padding: responsive-size(10px, 20px);
      font-size: responsive-size(14px, 16px);
      line-height: responsive-size(18px, 24px);;
    }

    tr {
      th:first-child, td:first-child{
        padding-left: responsive-size(15px, 40px);
      }

      th:last-child, td:last-child {
        padding-right: responsive-size(15px, 40px);
      }
    }

    thead {
      border: 0;
      background: $blue-navy;
      color: $white;

      th {
        @extend h6;
        color: $white;

        @media (max-width: $tablet) {
          padding-top: 20px;
          padding-bottom: 20px;
        }
      }
    }

    tbody {
      tr {
        background: $white;

        &:nth-child(odd) {
          background: $light-grey;
        }
      }

      td {

      }
    }
  }

  // Button
  .wp-block-button__link {
    @extend .btn;
    @extend .-primary;
  }
  
  .is-style-secondary .wp-block-button__link {
    @extend .btn;
    @extend .-secondary;
  }

  // Quote
  .wp-block-quote {
    p{
      font-size: inherit;
      line-height: inherit;
      color: inherit !important; 
    }
  }

  // Heading custom:
  .is-style-with-line {
    padding-top: responsive-size(36px, 52px);
    position: relative;
    &::before {
      position: absolute;
      content: '';
      display: block;
      top: 0;
      left: 0;
      width: calc( 100vw - 2 * var(--container-margin) );
      height: 1px;
      background: $base-400;
      margin-bottom: 20px;
    }
  }

  // Accordion (wordpress native)
  // .wp-block-details {
  //   position: relative;
  //   border-bottom: 1px solid $black;

  //   summary {
  //     @extend h5;
  //     padding: 32px 32px 32px 0;
  //     margin: 0;
  //     display: flex;
  //     transition: $transition;
      
  //     &::marker {
  //       display: none;
  //     }
      
  //     &::before {
  //       @include icon('\e909'); // carret icon
  //       transition: $transition;
  //       font-size: responsive-size(12px, 12px);
  //       right: 0;
  //     }
  //   }


  //   // all child except summary
  //   *:not(summary) {
  //     margin-block-start: 0em;
  //     margin-block-end: 2em;
  //   }

  //   // open
  //   &[open] {
  //     summary {
  //       color: $black;
        
  //       &::before {
  //         transform: rotate(180deg);
  //       }
  //     }
  //   }
  // }

  // // // // // 
  // Pattern
  // // // // //

  .group-text-image{
    .wp-block-columns {
      column-gap: grid-space(math.div(2,12));
    }

    @media (max-width: $tablet-lg) {
      .wp-block-columns {
        .column-image{
          order: -1;
        }
      }
    }
  }

  .group-background{
    .wp-block-columns {
      column-gap: grid-space(math.div(1,12));
    }
  }
}
