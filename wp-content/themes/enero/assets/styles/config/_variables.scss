/* Set Variable to :root DOM */
:root {
    --header-height: 100px;

    // Font family
    --font-primary: "Rubik", sans-serif;
    --font-title: "Poppins", sans-serif;
    --font-cta: "Poppins", sans-serif;

    // Colors
    --color-title: #{$blue-dark};
    --color-title-secondary: #{$blue-dark-40};

    --color-text: #{$base-700};
    --color-text-secondary: #{$base-600};
    --color-sticky-title: #{$blue-primary};
    --color-bg: #{$bg};

    --border-default: #{$base-400};
    --border-strong: #{$base-700};

    --cta-primary: #{$blue-dark};

    --eyebrow: #{$blue-primary};

    --form-select: #{$base-600};
    --form-placeholder: #{$base-500};

    // Colors brand
    --brand-blue-primary: #{$blue-primary};
    --brand-blue-dark: #{$blue-dark};
    --brand-blue-dark-60: #{$blue-dark-60};
    --brand-blue-navy: #{$blue-navy};
    --brand-blue-steel: #{$blue-steel};
    --brand-blue-royal: #{$blue-royal};
    --brand-teal-soft: #{$teal-soft};
    --brand-light-grey: #{$light-grey};
    --brand-light-grey-50: #{$light-grey-50};

    // Base
    --base-800: #{$base-800};
    --base-700: #{$base-700};
    --base-600: #{$base-600};
    --base-500: #{$base-500};
    --base-400: #{$base-400};
    --base-100: #{$base-100};


    // Body
    --font-size-large:      #{responsive-size(22px, 24px, $desktop-lg)};
    --line-height-large:    146%;

    --font-size-body:       #{responsive-size(16px, 19px, $desktop-lg)};
    --line-height-body:     150%;

    --font-size-small:      #{responsive-size(14px, 15px, $desktop-lg)};
    --line-height-small:    146%;

    --font-size-caption:    #{responsive-size(13px, 14px, $desktop-lg)};
    --line-height-caption:  146%;

    // Headings
    --font-size-h1:       #{responsive-size(48px, 72px, $desktop-lg)};
    --font-size-h1-small: #{responsive-size(45px, 64px, $desktop-lg)};
    --font-size-h2-large: #{responsive-size(40px, 56px, $desktop-lg)};
    --font-size-h2:       #{responsive-size(40px, 48px, $desktop-lg)};
    --font-size-h2-small: #{responsive-size(32px, 42px, $desktop-lg)};
    --font-size-h3:       #{responsive-size(32px, 36px, $desktop-lg)};
    --font-size-h4:       #{responsive-size(24px, 30px, $desktop-lg)};
    --font-size-h5:       #{responsive-size(20px, 24px, $desktop-lg)};
    --font-size-h6:       #{responsive-size(18px, 22px, $desktop-lg)};

    --line-height-h1:       118%;
    --line-height-h1-small: 118%;
    --line-height-h2-large: 118%;
    --line-height-h2:       118%;
    --line-height-h2-small: 128%;
    --line-height-h3:       118%;
    --line-height-h4:       118%;
    --line-height-h5:       118%;
    --line-height-h6:       118%;

    // Eyebrows
    --font-size-eyebrow:            #{responsive-size(20px, 28px, $desktop-lg)};
    --line-height-eyebrow:          100%;

    --font-size-eyebrow-small:      #{responsive-size(16px, 18px, $desktop-lg)};
    --line-height-eyebrow-small:    100%;

    // Meta
    --font-size-meta: #{rem(14px)};
    --line-height-meta: 140%;

    // CTA
    --font-size-cta-large:      #{responsive-size(18px, 20px, $desktop-lg)};
    --line-height-cta-large:    100%;

    --font-size-cta:            #{responsive-size(14px, 18px, $desktop-lg)};
    --line-height-cta:          100%;

    --font-size-cta-small:      #{responsive-size(16px, 16px, $desktop-lg)};
    --line-height-cta-small:    100%;

    // Tags 
    --font-size-tag:            #{responsive-size(15px, 18px, $desktop-lg)};
    --line-height-tag:          100%;

    --font-size-tag-large:      #{responsive-size(18px, 20px, $desktop-lg)};
    --line-height-tag-large:    100%;

    // Link
    --color-link: #{$blue-primary};
    --color-link-hover: #{$blue-dark};

    // Btn
    --color-btn-primary: #{$blue-primary};
    --color-btn-secondary: transparent;
    --color-btn-text: #{$white};
    --color-btn-text-secondary: #{$blue-dark};
    --color-btn-circle-primary: #{$white};
    --color-btn-circle-secondary: #{$blue-primary};

    --btn-size: var(--font-size-cta-small);

    // Gutenberg spacing
    --spacing-small: #{rem(20px)};
    --spacing-normal: #{rs(32px, 56px)};
    --spacing-medium: #{rs(48px, 80px)};
    --spacing-large: #{rs(80px, 120px)};
    --spacing-xlarge: #{rs(120px, 240px)};
    
    // Metrics
    --unit: #{$column-gap};
    --container-margin: 72px;

    // Grid
    --grid-columns: #{$base-column-nb};
    --grid-gutter: var(--unit);
    --grid-margin: var(--container-margin);

    // Radius
    --border-radius-large: #{rem(40px)};
    --border-radius-medium: #{rem(24px)};
    --border-radius-small: #{rem(12px)};

    // Set a max-width for the container (useful for large screens)
    @media screen and (min-width: $desktop-xxlg) {
        --container-margin: 10vw;
    }

    @media screen and (max-width: $tablet) {
        --unit: 20px;
        --grid-columns: 4;

        --container-margin: 20px;
    }
}

.-dark{
    @extend %dark-vars;
}