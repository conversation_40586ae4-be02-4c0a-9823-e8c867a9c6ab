/** Config variables
*** Add new ones if needed
**/

// Easing - Animation
$default-speed: 1s;
$default-easing: cubic-bezier(0.200, 0.600, 0.400, 1.000);
$transition: all $default-speed $default-easing;

// Border radius
$radius-sm: 12px;
$radius-md: 24px;
$radius-lg: 40px;

// Responsive
$desktop-xxlg: 2560px;
$desktop-xlg: 1920px;
$desktop-lg: 1600px;
$desktop: 1440px;
$desktop-sm: 1280px;

$tablet-max: 1024px;
$tablet-lg: 992px;
$tablet: 768px;
$tablet-sm: 600px;

$mobile-lg: 540px;
$mobile: 420px;
$mobile-sm: 360px;
$mobile-min: 320px;

// Grid
$base-column-nb: 12; 
$column-gap: 20px; 