import { module } from 'modujs';

export default class extends module {

    constructor(m) {
        super(m);

        this.events = {
            click: {
                button: 'toggleAccordion'
            }
        }
    }

    toggleAccordion() {
        const isOpen = this.el.classList.toggle('-open');
        this.el.setAttribute('aria-expanded', isOpen ? "true" : "false");
        this.inner.style.height = isOpen ? this.height + 'px' : 0;
    }

    init() {

        // Get content height and then collapse it
        this.inner = this.el.querySelector('.inner');
        this.height = this.inner.offsetHeight;
        this.inner.style.height = '0px';
    }
    
    destroy() {
    }
}