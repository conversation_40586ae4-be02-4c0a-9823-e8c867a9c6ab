import { module } from 'modujs';
import { gsap } from "gsap";
import { ScrollTrigger } from "gsap/ScrollTrigger";

gsap.registerPlugin(ScrollTrigger);


export default class extends module {

    constructor(m) {
        super(m);
        this.$title = this.el.querySelector('.content-sticky-title');
        this.$image = this.el.querySelector('.image-sticky');
        this.$content = this.el.querySelector('.content-sticky-content');
        this.$contentImage = this.el.querySelector('.content-sticky-image');

    }
    
    init() {

        this.mm = gsap.matchMedia();

        // Set Sticky animation on scroll 
        this.st = ScrollTrigger.create({
            trigger: this.$title,
            start: "top top",
            endTrigger: this.el,
            end: () => `bottom ${this.$image? this.$contentImage.offsetHeight : 0 + this.$title.offsetHeight}px`,
            pin: true,
            pinSpacing: false,
            anticipatePin: 1,
            // markers: true,
        });

        if (this.$image) {
            this.mm.add(
                "(min-width: 992px)", () => {
                    this.st_image = ScrollTrigger.create({
                        trigger: this.$title,
                        start: "top top",
                        pin: this.$image,
                        endTrigger: this.el,
                        end: () => `bottom ${this.$contentImage.offsetHeight + this.$title.offsetHeight}px`,
                        pinSpacing: false,
                        anticipatePin: 1,
                        // markers: true,
                    });
                }
            );
        }
    }

    

    // Method to destroy the instance (Modular hook)
    destroy() {
        this.st.kill();
    }
}