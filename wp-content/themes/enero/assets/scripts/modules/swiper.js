import { module } from 'modujs';
import { debounce } from "./../utils/tickers"; // Importing debounce utility function
import Swiper from 'swiper';
import { Autoplay, Navigation, EffectFade, Pagination } from 'swiper/modules';

Swiper.use([Navigation, Autoplay, EffectFade, Pagination]);

export default class extends module {
    
    constructor(m) {
        super(m);

        this.events = {
            //If you need Hover effect
            // mouseenter : {
            //     swiper: 'zoneHover', 
            // },
            // mouseleave : {
            //     swiper: 'zoneHover', 
            // },
        }

        this.$slider = this.el;
        this.$nextBtn = this.$('next')[0];
        this.$prevBtn = this.$('prev')[0];

    }

    /**
     * Resolves CSS variable to its computed value
     */
    resolveCSSVariable(value) {
        if (typeof value === 'string' && value.startsWith('var(')) {
            // Extract the CSS variable name from var(--variable-name)
            const variableName = value.match(/var\(([^)]+)\)/)[1];
            // Get the computed value from the root element
            const computedValue = getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
            // Parse the numeric value (remove 'px', 'rem', etc.)
            return parseFloat(computedValue) || 0;
        }
        return parseInt(value, 10) || 0;
    }

    /**
     * Gets Swiper parameters from data-attributes
     */
    getSwiperOptions() {
        const dataset = this.$slider.dataset;
        const options = {
            speed: dataset.speed ? parseInt(dataset.speed, 10) : 300,
            loop: dataset.loop === "true",
            effect: dataset.effect || 'slide',
            slidesPerView: dataset.slidesPerView ? parseFloat(dataset.slidesPerView) : 2.5,
            spaceBetween: dataset.spaceBetween ? this.resolveCSSVariable(dataset.spaceBetween) : 0,
            breakpoints: dataset.breakpoints ? JSON.parse(dataset.breakpoints) : {},
            navigation: {
                nextEl: this.$nextBtn,
                prevEl: this.$prevBtn,
            }
        };
    
        if (dataset.autoplay) {
            options.autoplay = {
                delay: parseInt(dataset.autoplay, 10),
                disableOnInteraction: true,
            };
        }
    
        return options;
    }
    

    init() {

        const swiperContainer = this.$slider.querySelector('.swiper-container');
        const options = this.getSwiperOptions(); // Get options bye attribute

        this.swiper = new Swiper(swiperContainer, options);

        // Adding focus event listener to update Swiper instance based on focused element.
        this.$slider.addEventListener('focusin', (event) => {
            const focusedElement = event.target.closest('.swiper-slide');
            if (focusedElement) {
                const slideIndex = Array.from(swiperContainer.querySelectorAll('.swiper-slide')).indexOf(focusedElement);
                if (slideIndex >= 0) {
                    this.swiper.slideTo(slideIndex);
                }
            }
        });

    }

    /**
     * Show or Hide Mouse tracker
     */
    zoneHover(e) {
        const track = {
            display: e.type === 'mouseenter',
            target: '.slider'
        };
        this.call('toggleDisplay', track, 'mouseTracker');
    }

    destroy() {
        this.swiper.destroy();
    }
}