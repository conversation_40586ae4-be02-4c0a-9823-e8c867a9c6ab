<?php

$context = Timber::context();
$timber_post = Timber::get_post();


$args = [
    'post_type'      => 'process',
    'post_status'    => 'publish',
    'posts_per_page' => 12,
    'orderby'        => 'date',
    'order'          => 'DESC',
    // todo add more args here
];

$query = new WP_Query($args);

$related_processes = new Timber\PostQuery( $query );

$args = [
    'post_type'      => 'work',
    'post_status'    => 'publish',
    'posts_per_page' => 12,
    'orderby'        => 'date',
    'order'          => 'DESC',
    // todo add more args here
];

$query = new WP_Query($args);

$related_works = new Timber\PostQuery( $query );

$context['post'] = $timber_post;
$context['related_processes'] = $related_processes;
$context['related_works'] = $related_works;

// dd($context['related_process'], $context['related_works']);

Timber::render( array( 'pages/singles/single-industry-sheet.twig' ), $context );
