<?php
/**
 * Disable <PERSON><PERSON><PERSON> by template
 *
 */
function disable_gutenberg( $can_edit ) {
	if ( ! ( is_admin() && ! empty( $_GET['post'] ) ) ) {
		return $can_edit;
	}
	if ( should_disable( $_GET['post'] ) ) {
		$can_edit = false;
	}

	return $can_edit;
}

function gutenberg_allowed_block_types( $allowed_blocks ) {

	return array(
		'core/image',
		'core/paragraph',
		'core/heading',
		'core/list',
		'core/list-item',
		'core/gallery',
		'core/quote',
		'core/cover',
		'core/file',
		'core/video',
		'core/table',
		'core/code',
		'core/freeform',
		'core/html',
		'core/preformatted',
		'core/button',
		'core/buttons',
		'core/column',
		'core/columns',
		'core/text-columns',
		'core/media-text',
		'core/separator',
		'core/embed',
		'core/spacer',
		'core/shortcode',
		'core/group',
		'core/embed/youtube',
		'core/details',

		// ACF 
		'acf/slider',
		'acf/accordion',

		// GF
		// 'gravityforms/form',

		// Kryzalid 
		"kryzalid/sticky-content",
		"kryzalid/accordion",
	);
}

/**
 * Templates and Page IDs without editor
 *
 */
function should_disable( $id = false ) {
	if ( empty( $id ) ) {
		return false;
	}
	$disabled_ids = [
		//get_option( 'page_on_front' ),
	];
	$disabled_types = [
		//'council_member',
	];
	$disabled_templates = [
		//'templates/landing.php',
	];

	return in_array( intval( $id ), $disabled_ids ) || in_array( get_post_type( $id ), $disabled_types ) || in_array( get_page_template_slug( $id ), $disabled_templates );
}


/**
 * Add custom patterns
 *
 */
function kryzaplate_register_patterns() {

	remove_theme_support( 'core-block-patterns' );

	register_block_pattern_category(
		'enero',
		array( 'label' => __( 'Enero', 'enero' ) )
	);

	register_block_pattern(
		'enero/custom-pattern',
		array(
			'title'       => __( 'Groupe texte/image', 'enero' ),
			'description' => _x( 'Groupe texte/image pattern', 'Groupe personnalisé Texte/image pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"className":"group-text-image","layout":{"type":"default"}} -->
			<div class="wp-block-group group-text-image"><!-- wp:separator -->
			<hr class="wp-block-separator has-alpha-channel-opacity"/>
			<!-- /wp:separator -->

			<!-- wp:columns {"verticalAlignment":"top"} -->
			<div class="wp-block-columns are-vertically-aligned-top"><!-- wp:column {"verticalAlignment":"top"} -->
			<div class="wp-block-column is-vertically-aligned-top"><!-- wp:heading -->
			<h2 class="wp-block-heading">Plusieurs modèles de disposition seront disponibles</h2>
			<!-- /wp:heading -->

			<!-- wp:paragraph {"style":{"spacing":{"margin":{"top":"var:preset|spacing|medium"}}}} -->
			<p style="margin-top:var(--wp--preset--spacing--medium)">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nulla bibendum vitae nisi at efficitur. Morbi nec velit purus. Mauris nec suscipit turpis. Interdum et malesuada fames ac ante ipsum primis in faucibus. Nulla et luctus augue, tincidunt efficitur mauris. Nam rhoncus mi quam, sit amet interdum sapien viverra sit amet. Praesent aliquam, tortor ac tincidunt maximus, tortor sapien scelerisque velit, in dictum enim velit at ante.</p>
			<!-- /wp:paragraph --></div>
			<!-- /wp:column -->

			<!-- wp:column {"verticalAlignment":"top","className":"column-image"} -->
			<div class="wp-block-column is-vertically-aligned-top column-image"><!-- wp:image {"id":119,"sizeSlug":"large","linkDestination":"none"} -->
			<figure class="wp-block-image size-large"><img src="http://enero.local/wp-content/uploads/2025/09/placeholder_03-1024x1024.webp" alt="" class="wp-image-119"/></figure>
			<!-- /wp:image --></div>
			<!-- /wp:column --></div>
			<!-- /wp:columns --></div>
			<!-- /wp:group -->',
		)
	);
	
	register_block_pattern(
		'enero/custom-pattern-2',
		array(
			'title'       => __( 'Groupe boite avec image', 'enero' ),
			'description' => _x( 'Groupe boite avec image pattern', 'Groupe personnalisé boite avec fond de couleur et image pour le theme Enero', 'enero' ),
			'categories'  => array( 'enero' ),
			'content' 	  => '<!-- wp:group {"className":"group-background","backgroundColor":"brand-blue-steel","layout":{"type":"constrained"}} -->
			<div class="wp-block-group group-background has-brand-blue-steel-background-color has-background"><!-- wp:columns -->
			<div class="wp-block-columns"><!-- wp:column {"width":"66.66%"} -->
			<div class="wp-block-column" style="flex-basis:66.66%"><!-- wp:heading -->
			<h2 class="wp-block-heading">Possiblité de boite avec image</h2>
			<!-- /wp:heading -->

			<!-- wp:paragraph {"style":{"spacing":{"margin":{"top":"var:preset|spacing|normal"}}}} -->
			<p style="margin-top:var(--wp--preset--spacing--normal)">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas sed nisl eros. Sed a sapien sapien. Nulla fermentum, erat et molestie tristique, felis nisl rutrum mauris, tempor consectetur nisi lorem non purus.</p>
			<!-- /wp:paragraph -->

			<!-- wp:paragraph {"style":{"spacing":{"margin":{"bottom":"var:preset|spacing|normal"}}}} -->
			<p style="margin-bottom:var(--wp--preset--spacing--normal)">Lorem ipsum dolor sit amet, consectetur adipiscing elit. Maecenas sed nisl eros. Sed a sapien sapien. Nulla fermentum, erat et molestie tristique, felis nisl rutrum mauris, tempor consectetur nisi lorem non purus.</p>
			<!-- /wp:paragraph -->

			<!-- wp:buttons -->
			<div class="wp-block-buttons"><!-- wp:button {"className":"is-style-primary"} -->
			<div class="wp-block-button is-style-primary"><a class="wp-block-button__link wp-element-button">Bouton primaire</a></div>
			<!-- /wp:button -->

			<!-- wp:button {"className":"is-style-secondary"} -->
			<div class="wp-block-button is-style-secondary"><a class="wp-block-button__link wp-element-button">Bouton secondaire</a></div>
			<!-- /wp:button --></div>
			<!-- /wp:buttons --></div>
			<!-- /wp:column -->

			<!-- wp:column {"width":"33.33%"} -->
			<div class="wp-block-column" style="flex-basis:33.33%"><!-- wp:image {"id":119,"sizeSlug":"large","linkDestination":"none"} -->
			<figure class="wp-block-image size-large"><img src="http://enero.local/wp-content/uploads/2025/09/placeholder_03-1024x1024.webp" alt="" class="wp-image-119"/></figure>
			<!-- /wp:image --></div>
			<!-- /wp:column --></div>
			<!-- /wp:columns --></div>
			<!-- /wp:group -->',
		)
	);
}